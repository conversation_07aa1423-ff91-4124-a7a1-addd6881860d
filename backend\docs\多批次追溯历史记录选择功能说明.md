# 多批次追溯历史记录选择功能说明

## 🎯 **功能背景**

### **问题描述**：
用户反馈："如果这个成品做了很多批比如10521080，那外卖输入这个编码，那怎么看以前的，销售订单号是一对一的，但是成品编码是一样的"

### **核心问题**：
- **一个产品编码对应多个生产批次** - 同一个产品（如10521080脚踏开关）会有多个生产批次
- **原有查询只显示最新记录** - 用户无法方便地查看历史批次的追溯信息
- **销售订单号是唯一的** - 但产品编码会重复使用

## ✅ **解决方案**

### **新增历史记录选择功能**：
当用户输入产品编码查询时，如果找到多个追溯记录，系统会显示一个历史记录选择对话框，让用户选择要查看的具体批次。

## 🔧 **功能实现**

### **1. 前端交互优化** 🖥️

#### **查询逻辑改进**：
```javascript
// 原有逻辑：只返回第一个找到的记录
if (result.data.list.length > 0) {
  const chainId = result.data.list[0].id  // 只取第一个
}

// 新逻辑：多个记录时显示选择对话框
if (result.data.list.length > 1) {
  historyList.value = result.data.list
  showHistoryDialog.value = true  // 显示选择对话框
  return
}
```

#### **历史记录选择对话框**：
- **表格展示**: 显示所有匹配的追溯记录
- **关键信息**: 链路编号、产品名称、批次号、状态、创建时间
- **交互方式**: 点击行或点击"查看详情"按钮选择记录
- **状态标识**: 用不同颜色标签区分已完成/进行中/待开始状态

### **2. 数据结构设计** 📊

#### **历史记录列表字段**：
```javascript
{
  id: 50,                           // 链路ID
  chain_no: "TC20250707COMPLETE",   // 追溯链路编号
  product_code: "10521080",         // 产品编码
  product_name: "脚踏开关",          // 产品名称
  batch_number: "PROD-FULL-001",    // 批次号
  status: "completed",              // 状态
  created_at: "2025-07-07T14:20:03" // 创建时间
}
```

#### **状态映射**：
- `completed` → "已完成" (绿色标签)
- `in_progress` → "进行中" (橙色标签)
- `pending` → "待开始" (灰色标签)

### **3. 用户体验设计** 🎨

#### **智能判断**：
- **单个记录**: 直接显示追溯详情
- **多个记录**: 显示历史记录选择对话框
- **无记录**: 显示"暂无数据"提示

#### **信息提示**：
```
找到 8 个产品编码为 '10521080' 的追溯记录，请选择要查看的记录：
```

#### **表格交互**：
- **行点击**: 直接选择该记录
- **按钮点击**: 明确的"查看详情"操作
- **高亮显示**: 鼠标悬停时高亮当前行

## 🧪 **测试验证**

### **测试数据**：
为产品编码 `10521080` 创建了8个不同批次的追溯记录：

| **序号** | **链路编号** | **批次号** | **状态** | **创建时间** |
|----------|-------------|------------|----------|-------------|
| 1 | TC20250707BATCH02 | PROD-BATCH-002 | completed | 2025/7/7 14:48:25 |
| 2 | TC20250707BATCH03 | PROD-BATCH-003 | in_progress | 2025/7/7 14:48:25 |
| 3 | TC20250707COMPLETE | PROD-FULL-001 | completed | 2025/7/7 14:20:03 |
| 4 | TC20250707FULL | PROD-FULL-001 | completed | 2025/7/7 14:18:43 |
| 5 | TC20250707021 | TEST-BATCH-002 | in_progress | 2025/7/7 13:23:17 |
| 6 | TC20250707011 | TEST-BATCH-001 | in_progress | 2025/7/7 13:09:12 |
| 7 | TC20250707001 | OB-SO250707003 | in_progress | 2025/7/7 13:04:39 |
| 8 | TC1751851684942697 | BATCH-10521080-684554 | in_progress | 2025/7/7 09:28:02 |

### **测试结果**：
✅ **查询成功**: 输入 `10521080` 找到8个追溯链路  
✅ **对话框显示**: 正确显示历史记录选择对话框  
✅ **记录选择**: 可以选择任意一个记录查看详情  
✅ **详情加载**: 选中记录的详细信息正确加载  

## 📋 **使用场景**

### **场景1: 质量问题追溯** 🔍
```
用户发现某批次产品有质量问题
→ 输入产品编码 10521080
→ 系统显示所有历史批次
→ 用户选择问题批次 PROD-BATCH-002
→ 查看该批次的完整追溯信息
→ 快速定位问题环节和责任人
```

### **场景2: 生产历史分析** 📊
```
管理者需要分析某产品的生产历史
→ 输入产品编码 10521080
→ 查看所有生产批次的时间线
→ 对比不同批次的生产效率
→ 分析生产改进趋势
```

### **场景3: 客户投诉处理** 📞
```
客户投诉某批次产品有问题
→ 根据销售记录确定批次号
→ 输入产品编码查看历史记录
→ 选择对应批次查看追溯信息
→ 快速响应客户并提供解决方案
```

## 🎯 **功能优势**

### **1. 解决核心痛点** ✅
- **历史记录可见**: 用户可以查看所有历史批次
- **精确选择**: 可以选择特定批次进行追溯
- **信息完整**: 每个批次的追溯信息都完整保留

### **2. 用户体验优化** ✅
- **智能判断**: 单个记录直接显示，多个记录显示选择
- **清晰展示**: 表格形式清晰展示所有选项
- **快速操作**: 点击即可选择，操作简单直观

### **3. 业务价值提升** ✅
- **质量管控**: 支持精确的质量问题追溯
- **生产分析**: 便于生产历史数据分析
- **客户服务**: 提高客户投诉处理效率

## 🔄 **查询策略优化**

### **新的查询优先级**：
1. **🆕 多记录判断** - 检查是否有多个匹配记录
2. **追溯链路编号查询** - TC开头的精确匹配
3. **产品编码查询** - 支持历史记录选择
4. **销售订单号查询** - 精确匹配单个记录
5. **批次号查询** - 批次相关查询
6. **通用搜索** - 兜底查询

### **智能化处理**：
```javascript
// 产品编码查询逻辑
if (result.data.list.length > 1) {
  // 多个记录：显示选择对话框
  showHistoryDialog.value = true
} else if (result.data.list.length === 1) {
  // 单个记录：直接显示详情
  loadTraceabilityDetail(result.data.list[0].id)
} else {
  // 无记录：继续其他查询方式
  tryOtherSearchMethods()
}
```

## 🎉 **功能总结**

### **解决的问题**：
- ✅ **多批次查看**: 解决了同一产品编码多个批次的查看问题
- ✅ **历史追溯**: 用户可以方便地查看任意历史批次
- ✅ **精确定位**: 支持精确到特定批次的质量追溯

### **技术实现**：
- ✅ **前端交互**: 新增历史记录选择对话框
- ✅ **查询优化**: 智能判断单个/多个记录的处理逻辑
- ✅ **用户体验**: 清晰的表格展示和简单的选择操作

### **业务价值**：
- ✅ **质量管理**: 支持精确的批次级质量追溯
- ✅ **生产分析**: 便于历史生产数据的对比分析
- ✅ **客户服务**: 提高问题处理的准确性和效率

现在用户输入产品编码 `10521080` 时，系统会智能地显示所有相关的历史批次，用户可以选择任意一个批次查看完整的追溯信息，完美解决了多批次追溯查看的问题！🎯
