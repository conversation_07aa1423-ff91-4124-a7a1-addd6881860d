{"version": 3, "file": "ParserOptions.js", "sourceRoot": "", "sources": ["../../src/ParserOptions.ts"], "names": [], "mappings": ";;;;;;AAAA,8EAA+C;AAC/C,gEAAiC;AAuBjC,MAAa,aAAa;IA+CtB,YAAmB,IAAwB;;QA5C3B,eAAU,GAAY,IAAI,CAAC;QAE3B,cAAS,GAAW,GAAG,CAAC;QAExB,gBAAW,GAAY,KAAK,CAAC;QAE7B,UAAK,GAAkB,GAAG,CAAC;QAE3B,WAAM,GAAkB,IAAI,CAAC;QAE7B,eAAU,GAAkB,IAAI,CAAC,KAAK,CAAC;QAEvC,YAAO,GAAkB,IAAI,CAAC;QAE9B,qBAAgB,GAAY,KAAK,CAAC;QAElC,UAAK,GAAY,KAAK,CAAC;QAEvB,UAAK,GAAY,KAAK,CAAC;QAEvB,SAAI,GAAY,KAAK,CAAC;QAEtB,YAAO,GAA2D,IAAI,CAAC;QAEvE,kBAAa,GAAY,KAAK,CAAC;QAE/B,yBAAoB,GAAY,KAAK,CAAC;QAEtC,2BAAsB,GAAY,KAAK,CAAC;QAExC,mBAAc,GAAW,IAAI,CAAC;QAI9B,aAAQ,GAAmB,MAAM,CAAC;QAElC,cAAS,GAAY,KAAK,CAAC;QAE3B,YAAO,GAAW,CAAC,CAAC;QAEpB,cAAS,GAAW,CAAC,CAAC;QAEtB,aAAQ,GAAW,CAAC,CAAC;QAGjC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,gBAAgB,GAAG,6BAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,SAAG,IAAI,CAAC,MAAM,mCAAI,IAAI,CAAC,KAAK,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,CAAC,sBAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,IAAI,MAAM,CAAC,0BAA0B,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;QAExF,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACzB;IACL,CAAC;CACJ;AA7DD,sCA6DC"}