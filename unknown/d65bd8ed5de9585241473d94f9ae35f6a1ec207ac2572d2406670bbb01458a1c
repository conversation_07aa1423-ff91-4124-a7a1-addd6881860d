{"version": 3, "file": "ColumnFormatter.js", "sourceRoot": "", "sources": ["../../../../src/parser/column/ColumnFormatter.ts"], "names": [], "mappings": ";;;AAEA,MAAa,eAAe;IAGxB,YAAmB,aAA4B;QAC3C,IAAI,aAAa,CAAC,IAAI,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;SACrD;aAAM,IAAI,aAAa,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;SACzD;aAAM,IAAI,aAAa,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;SAC1D;aAAM;YACH,IAAI,CAAC,MAAM,GAAG,CAAC,GAAW,EAAU,EAAE,CAAC,GAAG,CAAC;SAC9C;IACL,CAAC;CACJ;AAdD,0CAcC"}