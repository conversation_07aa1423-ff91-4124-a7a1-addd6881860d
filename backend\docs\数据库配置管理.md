# 数据库配置管理

## 📋 概述

系统已经统一了所有数据库配置，现在所有数据库连接都通过统一的配置文件管理，方便后期维护和数据库迁移。

## 🗂️ 配置文件结构

### 主配置文件
- `backend/src/config/database-config.js` - **统一的数据库配置管理中心**

### 使用配置的文件
- `backend/src/config/db.js` - 主数据库连接池
- `backend/src/config/sequelize.js` - Sequelize ORM配置
- `backend/src/database/index.js` - 备用数据库连接池
- `backend/src/config/index.js` - 系统配置
- 所有 `backend/src/scripts/*.js` 脚本文件（35个文件已更新）

## 🔧 配置方法

### 1. 环境变量配置（推荐）

复制 `.env.example` 为 `.env` 并修改：

```env
# 数据库配置
DB_HOST=你的数据库地址
DB_PORT=3306
DB_USER=数据库用户名
DB_PASSWORD=数据库密码
DB_NAME=数据库名称

# 连接池配置
DB_CONNECTION_LIMIT=15
DB_QUEUE_LIMIT=30
DB_CONNECT_TIMEOUT=60000
DB_MAX_IDLE=8
DB_IDLE_TIMEOUT=120000
```

### 2. 直接修改配置文件

如果不使用环境变量，可以直接修改 `backend/src/config/database-config.js` 中的默认值。

## 🚀 使用方法

### 在新脚本中使用

```javascript
const mysql = require('mysql2/promise');
const { getConnectionConfig } = require('../config/database-config');

async function yourScript() {
  const connection = await mysql.createConnection(getConnectionConfig());
  // 使用连接...
  await connection.end();
}
```

### 在控制器中使用

```javascript
const { getPoolConfig } = require('../config/database-config');
const mysql = require('mysql2/promise');

const pool = mysql.createPool(getPoolConfig());
```

## 📊 优化效果

### ✅ 已解决的问题
1. **消除重复配置** - 删除了多个重复的数据库配置文件
2. **统一管理** - 所有数据库连接现在使用同一个配置源
3. **便于维护** - 更换数据库只需修改一个地方
4. **环境隔离** - 支持通过环境变量区分开发/生产环境

### 📈 更新统计
- 删除重复配置文件：1个
- 更新主配置文件：4个
- 更新脚本文件：35个
- 新增统一配置文件：1个

## 🔄 数据库迁移指南

当需要更换数据库时，只需要：

1. **修改环境变量**：
   ```env
   DB_HOST=新的数据库地址
   DB_PORT=新的端口
   DB_USER=新的用户名
   DB_PASSWORD=新的密码
   DB_NAME=新的数据库名
   ```

2. **或者修改配置文件**：
   编辑 `backend/src/config/database-config.js` 中的 `DATABASE_CONFIG` 对象

3. **重启应用**：
   ```bash
   npm restart
   ```

## 🛡️ 安全建议

1. **使用环境变量** - 不要在代码中硬编码敏感信息
2. **保护 .env 文件** - 确保 .env 文件不被提交到版本控制
3. **定期更新密码** - 定期更换数据库密码
4. **限制连接数** - 根据服务器性能调整连接池大小

## 🔍 故障排除

### 连接失败
1. 检查数据库服务是否运行
2. 验证网络连接
3. 确认用户名密码正确
4. 检查防火墙设置

### 性能问题
1. 调整连接池大小 (`DB_CONNECTION_LIMIT`)
2. 优化空闲超时时间 (`DB_IDLE_TIMEOUT`)
3. 监控连接池使用情况

现在您的系统数据库配置已经完全统一和优化！
