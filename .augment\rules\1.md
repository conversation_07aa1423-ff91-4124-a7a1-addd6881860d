---
type: "agent_requested"
description: "Example description"
---
Always respond in Chinese-simplified
我开发的电脑是Windows命令使用Windows PowerShell
数据库连接信息
MySQL Configuration
DB_HOST=*********
DB_PORT=3306
DB_USER=root
DB_PASSWORD=mysql_ycMQCy
DB_NAME=mes
遵循架构设计，保持代码风格一致 代码修改遵循单一职责原则，
不混合多个变更 在进行代码设计规划的时候，
请符合"第一性原理" 在代码实现的时候，
请符合"KISS原则"和"SOLID原则" 
尽量复用已有代码，避免重复代码
获取追溯ID=117的物料记录: 2 条记录现在只有物料记录采购入库物料记录和出库记录，我想要从采购入库-检验-生产出库-成品入库-销售出库，闭环的追溯记录 