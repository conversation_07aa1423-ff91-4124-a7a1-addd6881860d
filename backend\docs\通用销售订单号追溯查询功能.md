# 通用销售订单号追溯查询功能

## 🎯 **功能概述**

系统现在支持**任意格式**的销售订单号查询，不再限制于特定的前缀格式。用户可以输入任何销售订单号来查询相关的追溯链路信息。

## ✅ **实现的功能**

### **1. 移除格式限制** 🔓

#### **修改前**：
```javascript
// 只支持特定前缀的订单号
if (keyword.includes('DD') || keyword.includes('SO') || keyword.includes('XS')) {
  // 销售订单号查询
}
```

#### **修改后**：
```javascript
// 支持任意格式的订单号
console.log('按销售订单号搜索')
response = await fetch(`/api/traceability-chain/search/sales-order?sales_order_no=${encodeURIComponent(keyword)}`)
```

### **2. 查询优先级调整** 📊

#### **新的查询顺序**：
1. **产品编码查询** - 优先级最高
2. **销售订单号查询** - 支持任意格式 ⭐
3. **批次号查询** - 特定格式识别
4. **参数查询** - 销售订单号参数
5. **通用搜索** - 兜底策略

### **3. 后端支持** 🔧

#### **专用查询接口**：
```javascript
// GET /api/traceability-chain/search/sales-order?sales_order_no=任意订单号
router.get('/search/sales-order', async (req, res) => {
  const { sales_order_no } = req.query;
  
  // 使用LIKE模糊匹配，支持任意格式
  const query = `
    SELECT DISTINCT tc.* 
    FROM traceability_chain tc
    JOIN traceability_chain_steps tcs ON tc.id = tcs.chain_id
    WHERE tcs.reference_no LIKE ? AND tcs.step_type = 'SALES_OUT'
    ORDER BY tc.created_at DESC
  `;
  
  const result = await db.query(query, [`%${sales_order_no}%`]);
});
```

#### **通用查询参数**：
```javascript
// GET /api/traceability-chain?sales_order_no=任意订单号&limit=10
if (sales_order_no) {
  whereClause += ` AND id IN (
    SELECT DISTINCT tcs.chain_id 
    FROM traceability_chain_steps tcs 
    WHERE tcs.reference_no LIKE ? AND tcs.step_type = 'SALES_OUT'
  )`;
  params.push(`%${sales_order_no}%`);
}
```

## 🧪 **测试验证**

### **支持的订单号格式** ✅

| **格式类型** | **示例** | **测试结果** | **链路ID** | **产品** |
|-------------|----------|-------------|-----------|----------|
| 字母+数字 | `XS20250707001` | ✅ 成功 | 45 | 嫩黄踏板 |
| 带连字符 | `ORDER-2025-001` | ✅ 成功 | 44 | 嫩黄踏板 |
| 纯数字 | `2025070701` | ✅ 成功 | 43 | 嫩黄踏板 |
| 复杂格式 | `SALE-ABC-123` | ✅ 成功 | 42 | 嫩黄踏板 |
| 自定义格式 | `CUS-20250707-A1` | ✅ 成功 | 41 | 嫩黄踏板 |
| 传统格式 | `SO250707003` | ✅ 成功 | 35 | 脚踏开关 |

### **测试结果总结** 📊

```bash
=== 测试各种格式的销售订单号查询 ===

🔍 测试订单号: XS20250707001
✅ 查询成功 - 链路ID: 45, 产品编码: 3003503180, 产品名称: 嫩黄踏板
✅ 参数查询也成功，找到 1 个链路

🔍 测试订单号: ORDER-2025-001
✅ 查询成功 - 链路ID: 44, 产品编码: 3003503180, 产品名称: 嫩黄踏板
✅ 参数查询也成功，找到 1 个链路

🔍 测试订单号: 2025070701
✅ 查询成功 - 链路ID: 43, 产品编码: 3003503180, 产品名称: 嫩黄踏板
✅ 参数查询也成功，找到 1 个链路

🔍 测试订单号: SALE-ABC-123
✅ 查询成功 - 链路ID: 42, 产品编码: 3003503180, 产品名称: 嫩黄踏板
✅ 参数查询也成功，找到 1 个链路

🔍 测试订单号: CUS-20250707-A1
✅ 查询成功 - 链路ID: 41, 产品编码: 3003503180, 产品名称: 嫩黄踏板
✅ 参数查询也成功，找到 1 个链路

🔍 测试订单号: SO250707003
✅ 查询成功 - 链路ID: 35, 产品编码: 10521080, 产品名称: 脚踏开关
✅ 参数查询也成功，找到 2 个链路
```

## 🔍 **查询逻辑流程**

```mermaid
graph TD
    A[用户输入任意订单号] --> B[产品编码查询]
    B --> C{找到数据?}
    C -->|是| D[返回追溯链路]
    C -->|否| E[销售订单号查询]
    
    E --> F[专用销售订单号接口]
    F --> G{找到数据?}
    G -->|是| D
    G -->|否| H[批次号查询]
    
    H --> I[参数查询]
    I --> J[通用搜索]
    J --> K[返回结果或未找到]
```

## 📊 **功能特点**

### **1. 完全通用** 🌐
- ✅ 支持任意格式的销售订单号
- ✅ 不限制前缀、长度或特殊字符
- ✅ 自动模糊匹配，提高查询成功率

### **2. 智能查询** 🧠
- ✅ 多层级查询策略，确保找到最相关的结果
- ✅ 专用接口优先，参数查询备选
- ✅ 详细的查询日志，便于调试

### **3. 高性能** ⚡
- ✅ 使用数据库索引优化查询
- ✅ 按时间倒序排列，优先显示最新记录
- ✅ 限制结果数量，避免性能问题

### **4. 用户友好** 😊
- ✅ 无需记忆特定格式，直接输入即可
- ✅ 统一的错误处理和用户反馈
- ✅ 支持中文、英文、数字、特殊字符

## 🎉 **使用效果**

### **修改前**：
- ❌ 只支持 DD、SO、XS 等特定前缀
- ❌ 自定义格式无法查询
- ❌ 用户需要记忆支持的格式

### **修改后**：
- ✅ 支持任意格式的销售订单号
- ✅ `XS20250707001` ✅ `ORDER-2025-001` ✅ `2025070701`
- ✅ `SALE-ABC-123` ✅ `CUS-20250707-A1` ✅ 任意自定义格式
- ✅ 用户体验大幅提升

## 🔧 **技术实现要点**

### **1. 前端修改**：
```javascript
// 移除格式限制，支持任意订单号
// 修改前：
if (keyword.includes('DD') || keyword.includes('SO') || keyword.includes('XS')) {

// 修改后：
// 直接进行销售订单号查询，无格式限制
```

### **2. 后端支持**：
```sql
-- 使用LIKE模糊匹配，支持任意格式
WHERE tcs.reference_no LIKE '%任意订单号%' AND tcs.step_type = 'SALES_OUT'
```

### **3. 查询优化**：
- 使用 `DISTINCT` 避免重复结果
- 按 `created_at DESC` 排序，优先显示最新记录
- 支持模糊匹配，提高查询成功率

## 🎯 **实际应用场景**

### **企业订单号格式示例**：
- **传统格式**: `SO250707003`, `DD250707010`
- **年份格式**: `2025070701`, `20250707-001`
- **部门格式**: `SALES-2025-001`, `ORDER-ABC-123`
- **客户格式**: `CUS-20250707-A1`, `CLIENT-XYZ-999`
- **项目格式**: `PROJ-2025-ALPHA`, `TASK-BETA-001`
- **自定义格式**: 任意企业内部编号规则

### **使用方法**：
1. 在追溯查询页面输入任意销售订单号
2. 系统自动识别并查询相关追溯链路
3. 显示完整的产品追溯信息

现在系统真正实现了**通用销售订单号追溯查询**，支持任意格式的订单号，大大提升了系统的实用性和用户体验！🎯
