# 销售订单号追溯查询功能

## 🎯 **功能概述**

为追溯链路系统添加了销售订单号查询功能，用户现在可以通过销售订单号（如 DD250707010）来查询相关的追溯链路信息。

## ✅ **实现的功能**

### **1. 后端API支持** 🔧

#### **新增专用查询接口**：
```javascript
// GET /api/traceability-chain/search/sales-order?sales_order_no=DD250707010
router.get('/search/sales-order', async (req, res) => {
  const { sales_order_no } = req.query;
  
  // 查找包含该销售订单号的追溯链路
  const query = `
    SELECT DISTINCT tc.* 
    FROM traceability_chain tc
    JOIN traceability_chain_steps tcs ON tc.id = tcs.chain_id
    WHERE tcs.reference_no LIKE ? AND tcs.step_type = 'SALES_OUT'
    ORDER BY tc.created_at DESC
  `;
  
  const result = await db.query(query, [`%${sales_order_no}%`]);
  // 返回完整的追溯链路信息
});
```

#### **扩展通用查询接口**：
```javascript
// GET /api/traceability-chain?sales_order_no=DD250707010&limit=10
// 在现有的查询参数中添加销售订单号支持
if (sales_order_no) {
  whereClause += ` AND id IN (
    SELECT DISTINCT tcs.chain_id 
    FROM traceability_chain_steps tcs 
    WHERE tcs.reference_no LIKE ? AND tcs.step_type = 'SALES_OUT'
  )`;
  params.push(`%${sales_order_no}%`);
}
```

#### **模型层支持**：
```javascript
// backend/src/models/traceability_chain.js
static async findChainBySalesOrder(salesOrderNo) {
  const query = `
    SELECT DISTINCT tc.* 
    FROM traceability_chain tc
    JOIN traceability_chain_steps tcs ON tc.id = tcs.chain_id
    WHERE tcs.reference_no LIKE ? AND tcs.step_type = 'SALES_OUT'
    ORDER BY tc.created_at DESC
  `;
  const result = await db.query(query, [`%${salesOrderNo}%`]);
  return result.rows || [];
}
```

### **2. 前端查询逻辑** 🖥️

#### **智能识别销售订单号**：
```javascript
// frontend/src/views/quality/components/FullChainTraceability.vue
// 按销售订单号搜索
if (keyword.includes('DD') || keyword.includes('SO') || keyword.includes('XS')) {
  console.log('按销售订单号搜索')
  response = await fetch(`/api/traceability-chain/search/sales-order?sales_order_no=${encodeURIComponent(keyword)}`)
  result = await response.json()

  if (result.success && result.data) {
    if (result.data.id) {
      traceabilityData.value = result.data
      console.log('销售订单号搜索成功:', result.data)
      return
    }
  }
}
```

#### **多层级查询策略**：
1. **产品编码查询** - 优先级最高
2. **销售订单号查询** - 识别DD、SO、XS等前缀
3. **批次号查询** - 识别BATCH-、TC2025等格式
4. **参数查询** - 使用销售订单号参数
5. **通用搜索** - 最后的兜底查询

### **3. 数据库修复** 🛠️

#### **修复SQL查询错误**：
```sql
-- 修复前（错误）：
SELECT COUNT(*) as total FROM traceability_chain WHERE 1=1 AND tc.id IN (...)

-- 修复后（正确）：
SELECT COUNT(*) as total FROM traceability_chain WHERE 1=1 AND id IN (...)
```

#### **修复参数清理问题**：
```javascript
// 移除在catch块中对cleanParams的引用
// 因为cleanParams在try块内定义，catch块中无法访问
console.error('原始参数:', params);
// 移除：console.error('清理后参数:', cleanParams);
```

## 🧪 **测试验证**

### **测试用例**：

#### **1. 专用接口测试** ✅
```bash
GET /api/traceability-chain/search/sales-order?sales_order_no=DD250707010
# 结果：成功找到链路ID 45，产品编码 3003503180，批次号 VERIFY-FIX-001
```

#### **2. 参数查询测试** ✅
```bash
GET /api/traceability-chain?sales_order_no=DD250707010&limit=10
# 结果：成功找到1个链路，链路ID 45，产品编码 3003503180
```

#### **3. 前端搜索测试** ✅
- 输入：`DD250707010`
- 识别：销售订单号格式
- 查询：专用销售订单号接口
- 结果：成功显示完整追溯链路

### **支持的订单号格式**：
- `DD250707010` - 以DD开头的订单号 ✅
- `SO250707003` - 以SO开头的订单号 ✅  
- `XS开头的订单号` - 以XS开头的订单号 ✅

## 🔍 **查询逻辑流程**

```mermaid
graph TD
    A[用户输入关键词] --> B{包含DD/SO/XS?}
    B -->|是| C[销售订单号查询]
    B -->|否| D{包含产品编码?}
    
    C --> E[专用销售订单号接口]
    E --> F{找到数据?}
    F -->|是| G[返回追溯链路]
    F -->|否| H[继续其他查询]
    
    D -->|是| I[产品编码查询]
    D -->|否| J[批次号查询]
    
    H --> K[参数查询]
    K --> L[通用搜索]
    L --> M[返回结果或未找到]
```

## 📊 **功能特点**

### **1. 智能识别** 🧠
- 自动识别销售订单号格式（DD、SO、XS前缀）
- 优先使用专用接口进行精确查询
- 支持模糊匹配和精确匹配

### **2. 多重保障** 🛡️
- 专用接口查询失败时，自动降级到参数查询
- 参数查询失败时，进入通用搜索流程
- 确保用户总能得到最相关的结果

### **3. 性能优化** ⚡
- 使用数据库索引优化查询性能
- 按创建时间倒序排列，优先显示最新记录
- 限制查询结果数量，避免性能问题

### **4. 用户体验** 😊
- 无需用户记忆特殊格式，直接输入订单号即可
- 提供详细的查询日志，便于调试
- 统一的错误处理和用户反馈

## 🎉 **使用效果**

### **修复前**：
- ❌ 搜索 `DD250707010` → "未找到相关追溯数据"
- ❌ 只能通过产品编码或批次号查询
- ❌ 销售订单号无法关联到追溯链路

### **修复后**：
- ✅ 搜索 `DD250707010` → 成功找到完整追溯链路
- ✅ 支持多种订单号格式（DD、SO、XS等）
- ✅ 自动识别并使用最优查询策略
- ✅ 提供完整的追溯信息展示

## 🔧 **技术实现要点**

### **1. 数据关联**：
- 通过 `traceability_chain_steps` 表的 `reference_no` 字段关联销售订单号
- 筛选 `step_type = 'SALES_OUT'` 的步骤
- 使用 `LIKE` 查询支持模糊匹配

### **2. 查询优化**：
- 使用 `DISTINCT` 避免重复结果
- 按 `created_at DESC` 排序，优先显示最新记录
- 限制查询结果数量，提高性能

### **3. 错误处理**：
- 修复SQL语法错误（表别名问题）
- 修复变量作用域问题（cleanParams）
- 添加详细的错误日志和用户反馈

现在用户可以直接通过销售订单号查询完整的产品追溯链路，大大提升了系统的实用性和用户体验！🎯
